'use client'

import React, {
  useEffect, useRef, useState,
} from 'react';
import { createPortal } from 'react-dom';
import Konva from 'konva';
import autosize from 'autosize';
import { VectorImageStyleGroups } from '@/common/constants';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Plus, X,
} from 'lucide-react';
import { useOutsideClick } from '@/common/hooks/useOutsideClick';
import {
  Button, TextArea,
} from '../../../atoms';
import { addCursorHandlers } from '../CanvasEditor';
import { useCanvasLoading } from '../CanvasLoadingContext';

interface VectorImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
}

type VectorOptionType = {
  option: string;
  label: string;
  substyle?: string;
}

const ColorPicker = ({
  initialColor,
  onColorSelect,
  position,
}: {
  initialColor: string;
  onColorSelect: (color: string) => void;
  position?: { x: number; y: number };
}) => {
  const [hue, setHue] = useState(0);
  const [saturation, setSaturation] = useState(100);
  const [lightness, setLightness] = useState(50);
  const [hexValue, setHexValue] = useState(initialColor);
  const [adjustedPosition, setAdjustedPosition] = useState({ x: 0, y: 0 });

  // Initialize color picker with the initial color
  useEffect(() => {
    if (initialColor) {
      setHexValue(initialColor);
      // Convert hex to HSL for the sliders
      const rgb = hexToRgb(initialColor);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      setHue(hsl.h);
      setSaturation(hsl.s);
      setLightness(hsl.l);
    }
  }, [initialColor]);

  // Calculate adjusted position to keep picker within viewport
  useEffect(() => {
    if (position) {
      const pickerWidth = 256; // w-64 = 16rem = 256px
      const pickerHeight = 300; // Approximate height of the picker
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const scrollX = window.scrollX;
      const scrollY = window.scrollY;

      let adjustedX = position.x + scrollX;
      let adjustedY = position.y + scrollY;

      // Adjust horizontal position if picker would go off-screen
      if (adjustedX + pickerWidth > viewportWidth + scrollX) {
        adjustedX = position.x - pickerWidth - 10 + scrollX; // Position to the left instead
      }

      // Adjust vertical position if picker would go off-screen
      if (adjustedY + pickerHeight > viewportHeight + scrollY) {
        adjustedY = viewportHeight + scrollY - pickerHeight - 10;
      }

      // Ensure picker doesn't go above the top of the viewport
      if (adjustedY < scrollY) {
        adjustedY = scrollY + 10;
      }

      setAdjustedPosition({ x: adjustedX, y: adjustedY });
    }
  }, [position]);

  const rgbToHsl = (r: number, g: number, b: number) => {
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100),
    };
  };

  const hslToHex = (h: number, s: number, l: number) => {
    l /= 100;
    const a = s * Math.min(l, 1 - l) / 100;
    const f = (n: number) => {
      const k = (n + h / 30) % 12;
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      return Math.round(255 * color).toString(16).padStart(2, '0');
    };
    return `#${f(0)}${f(8)}${f(4)}`;
  };

  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : {
      r: 0,
      g: 0,
      b: 0,
    };
  };

  const updateColor = (h: number, s: number, l: number) => {
    const hex = hslToHex(h, s, l);
    setHexValue(hex);
    onColorSelect(hex); // Automatically update color
  };

  const handleHueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHue = parseInt(e.target.value);
    setHue(newHue);
    updateColor(newHue, saturation, lightness);
  };

  const handleSaturationLightnessChange = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const newSaturation = (x / rect.width) * 100;
    const newLightness = 100 - (y / rect.height) * 100;
    setSaturation(newSaturation);
    setLightness(newLightness);
    updateColor(hue, newSaturation, newLightness);
  };

  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setHexValue(value);
    // Validate hex and update if valid
    if (/^#[0-9A-F]{6}$/i.test(value)) {
      onColorSelect(value);
    }
  };

  const rgb = hexToRgb(hexValue);

  const colorPickerContent = (
    <div
      className="fixed z-50 p-4 bg-neutral-800 border border-neutral-600 rounded-lg w-64 shadow-2xl"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
    >
      {/* Color Canvas */}
      <div
        className="w-full h-32 rounded cursor-crosshair relative mb-3"
        style={{
          background: `linear-gradient(to bottom, transparent, black), linear-gradient(to right, white, hsl(${hue}, 100%, 50%))`,
        }}
        onClick={handleSaturationLightnessChange}
      >
        <div
          className="absolute w-3 h-3 border-2 border-white rounded-full transform -translate-x-1/2 -translate-y-1/2"
          style={{
            left: `${saturation}%`,
            top: `${100 - lightness}%`,
          }}
        />
      </div>

      {/* Hue Slider */}
      <div className="mb-3">
        <input
          type="range"
          min="0"
          max="360"
          value={hue}
          onChange={handleHueChange}
          className="w-full h-3 rounded appearance-none cursor-pointer"
          style={{
            background: 'linear-gradient(to right, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000)',
          }}
        />
      </div>

      {/* Hex Input */}
      <div className="mb-3">
        <input
          type="text"
          value={hexValue}
          onChange={handleHexChange}
          className="w-full px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-white text-sm"
          placeholder="#000000"
        />
      </div>

      {/* RGB Values */}
      <div className="grid grid-cols-3 gap-2 text-xs mb-3">
        <div className="text-center">
          <div className="text-gray-400">R</div>
          <div className="text-white font-mono">{rgb.r}</div>
        </div>
        <div className="text-center">
          <div className="text-gray-400">G</div>
          <div className="text-white font-mono">{rgb.g}</div>
        </div>
        <div className="text-center">
          <div className="text-gray-400">B</div>
          <div className="text-white font-mono">{rgb.b}</div>
        </div>
      </div>
    </div>
  );

  return typeof window !== 'undefined' ? createPortal(colorPickerContent, document.body) : null;
};

const ColorSelector = ({
  selectedColors,
  onColorsChange,
}: {
  selectedColors: string[];
  onColorsChange: (colors: string[]) => void;
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [editingColorIndex, setEditingColorIndex] = useState<number | null>(null);
  const [pickerPosition, setPickerPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const colorPickerRef = useRef<HTMLDivElement>(null);

  // Generate a random color
  const generateRandomColor = () => {
    const hue = Math.floor(Math.random() * 360);
    const saturation = Math.floor(Math.random() * 50) + 50; // 50-100%
    const lightness = Math.floor(Math.random() * 40) + 30; // 30-70%

    const hslToHex = (h: number, s: number, l: number) => {
      l /= 100;
      const a = s * Math.min(l, 1 - l) / 100;
      const f = (n: number) => {
        const k = (n + h / 30) % 12;
        const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
        return Math.round(255 * color).toString(16).padStart(2, '0');
      };
      return `#${f(0)}${f(8)}${f(4)}`;
    };

    return hslToHex(hue, saturation, lightness);
  };

  const updateColor = (newColor: string) => {
    if (editingColorIndex !== null) {
      const updatedColors = [...selectedColors];
      updatedColors[editingColorIndex] = newColor;
      onColorsChange(updatedColors);
    }
  };

  const removeColor = (colorToRemove: string) => {
    onColorsChange(selectedColors.filter(c => c !== colorToRemove));
    setShowColorPicker(false);
    setEditingColorIndex(null);
  };

  const handleAddColorClick = (event: React.MouseEvent) => {
    // Add a random color by default
    const randomColor = generateRandomColor();
    const newColors = [...selectedColors, randomColor];
    onColorsChange(newColors);

    // Calculate position for color picker
    const rect = event.currentTarget.getBoundingClientRect();
    setPickerPosition({
      x: rect.right + 10,
      y: rect.top,
    });

    // Open color picker for the newly added color
    setEditingColorIndex(newColors.length - 1);
    setShowColorPicker(true);
  };

  const handleColorClick = (index: number, event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setPickerPosition({
      x: rect.right + 10, // Position to the right of the color square
      y: rect.top,
    });
    setEditingColorIndex(index);
    setShowColorPicker(true);
  };

  // Close color picker when clicking outside
  useOutsideClick({
    isVisible: showColorPicker,
    ref: colorPickerRef,
    callback: () => {
      setShowColorPicker(false);
      setEditingColorIndex(null);
    },
  });

  return (
    <div className="space-y-3">
      <div className="grid grid-cols-5 gap-2 flex-wrap">
        {selectedColors.map((color, index) => (
          <div key={index} className="relative w-full aspect-square rounded-xl group">
            <div
              className="w-full aspect-square rounded-xl"
              style={{ backgroundColor: color }}
            />
            <div
              className="absolute inset-0 bg-black/60 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
              onClick={(e) => handleColorClick(index, e)}
            >
              <span className="text-white text-xs font-medium">Change</span>
            </div>
            <button
              onClick={() => removeColor(color)}
              title="Remove color"
              className="absolute -top-1 -right-1 p-0.5 bg-violets-are-blue rounded-full shadow-sm z-10"
              type="button"
            >
              <X className="w-2.5 h-2.5 text-neutral-100" />
            </button>
          </div>
        ))}

        <button
          type="button"
          onClick={handleAddColorClick}
          title="Add color"
          className="w-16 h-16 rounded-xl border border-white/20 bg-white/5 hover:border-none hover:bg-gradient-to-tr from-han-purple to-tulip transition-colors flex items-center justify-center group"
        >
          <Plus className="w-6 h-6 text-white/60 group-hover:text-white transition-colors" />
        </button>
      </div>

      {showColorPicker && editingColorIndex !== null && (
        <div ref={colorPickerRef}>
          <ColorPicker
            initialColor={selectedColors[editingColorIndex]}
            onColorSelect={updateColor}
            position={pickerPosition}
          />
        </div>
      )}
    </div>
  );
};

const BackgroundColorSelector = ({
  selectedColor,
  onColorChange,
}: {
  selectedColor: string;
  onColorChange: (color: string) => void;
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [pickerPosition, setPickerPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const colorPickerRef = useRef<HTMLDivElement>(null);

  // Generate a random color
  const generateRandomColor = () => {
    const hue = Math.floor(Math.random() * 360);
    const saturation = Math.floor(Math.random() * 50) + 50; // 50-100%
    const lightness = Math.floor(Math.random() * 40) + 30; // 30-70%

    const hslToHex = (h: number, s: number, l: number) => {
      l /= 100;
      const a = s * Math.min(l, 1 - l) / 100;
      const f = (n: number) => {
        const k = (n + h / 30) % 12;
        const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
        return Math.round(255 * color).toString(16).padStart(2, '0');
      };
      return `#${f(0)}${f(8)}${f(4)}`;
    };

    return hslToHex(hue, saturation, lightness);
  };

  const selectColor = (color: string) => {
    onColorChange(color);
  };

  const removeColor = () => {
    onColorChange('');
    setShowColorPicker(false);
  };

  const handleAddColorClick = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setPickerPosition({
      x: rect.right + 10,
      y: rect.top,
    });
    // Add a random color by default
    const randomColor = generateRandomColor();
    onColorChange(randomColor);
    setShowColorPicker(true);
  };

  const handleChangeColorClick = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setPickerPosition({
      x: rect.right + 10,
      y: rect.top,
    });
    setShowColorPicker(true);
  };

  // Close color picker when clicking outside
  useOutsideClick({
    isVisible: showColorPicker,
    ref: colorPickerRef,
    callback: () => setShowColorPicker(false),
  });

  return (
    <div className="space-y-3">
      <div className="flex items-end gap-2">
        {selectedColor ? (
          <div className="relative w-16 h-16 rounded-xl group">
            <div
              className="w-16 h-16 rounded-xl"
              style={{ backgroundColor: selectedColor }}
            />
            <div
              className="absolute inset-0 bg-black/60 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
              onClick={handleChangeColorClick}
            >
              <span className="text-white text-xs font-medium">Change</span>
            </div>
            <button
              onClick={removeColor}
              title="Remove background color"
              className="absolute -top-1 -right-1 p-0.5 bg-violets-are-blue rounded-full shadow-sm z-10"
              type="button"
            >
              <X className="w-2.5 h-2.5 text-neutral-100" />
            </button>
          </div>
        ) : (
          <button
            type="button"
            onClick={handleAddColorClick}
            title="Add background color"
            className="w-16 h-16 rounded-xl border border-white/20 bg-white/5 hover:border-none hover:bg-gradient-to-tr from-han-purple to-tulip transition-colors flex items-center justify-center group"
          >
            <Plus className="w-6 h-6 text-white/60 group-hover:text-white transition-colors" />
          </button>
        )}
      </div>

      {showColorPicker && selectedColor && (
        <div ref={colorPickerRef}>
          <ColorPicker
            initialColor={selectedColor}
            onColorSelect={selectColor}
            position={pickerPosition}
          />
        </div>
      )}
    </div>
  );
};

export const VectorImagePanel = ({
  canvas,
  agentId,
  planId,
}: VectorImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<VectorOptionType | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [artisticLevel, setArtisticLevel] = useState(3);
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [backgroundColor, setBackgroundColor] = useState<string>('');
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();
  const {
    setVectorizing, loadingStates,
  } = useCanvasLoading();

  const hexToRgb = (hex: string): [number, number, number] => {
    const cleanHex = hex.replace('#', '');
    const bigint = parseInt(cleanHex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return [r, g, b];
  };

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: VectorOptionType) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setVectorizing(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/recraft-image`;

      
      let isLogoStyle = false;

      if (selectedStyle) {
        isLogoStyle = VectorImageStyleGroups.logos.styles.some(
          style => style.option === selectedStyle.option,
        );
      }

      const colorsArray = selectedColors.length > 0
        ? selectedColors.map(color => ({ rgb: hexToRgb(color) }))
        : undefined;

      const backgroundColorObj = backgroundColor
        ? { rgb: hexToRgb(backgroundColor) }
        : undefined;

      const requestBody = {
        prompt: imagePrompt,
        width: 1024,
        height: 1024,
        count: 1,
        model: isLogoStyle ? 'recraftv2' : 'recraftv3',
        style: isLogoStyle ? 'icon' : 'vector_illustration',
        substyle: selectedStyle?.substyle,
        styleId: selectedStyle?.option || '',
        seed: seed,
        controls: {
          artistic_level: isLogoStyle ? undefined : artisticLevel,
          colors: colorsArray,
          background_color: backgroundColorObj,
          no_text: false,
        },
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 422 && errorData.error?.includes('NSFW')) {
          throw new Error('Content flagged as inappropriate. Please try a different prompt.');
        }
        throw new Error(errorData.error || 'Failed to generate vector image');
      }

      const result = await response.json();

      if (result.success && result.images && result.images.length > 0) {
        const imageUrl = result.images[0];

        const svgResponse = await fetch(imageUrl);
        const svgString = await svgResponse.text();
        await addSvgToCanvas(svgString);
        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Vector image generated and added to canvas!');
      } else {
        throw new Error('No image data received from Recraft');
      }
    } catch (error: unknown) {
      console.error('Error generating vector image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate vector image. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setVectorizing(false);
    }
  };

  const addSvgToCanvas = async (svgString: string) => {
    if (!canvas) {
      return;
    }

    try {
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);

      const imageObj = new window.Image();
      imageObj.onload = () => {
        const konvaImage = new Konva.Image({
          image: imageObj,
          draggable: true,
        });

        let layer = canvas.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          canvas.add(layer);
        }

        const canvasWidth = canvas.width();
        const canvasHeight = canvas.height();
        const imageWidth = imageObj.naturalWidth;
        const imageHeight = imageObj.naturalHeight;

        let isLogoStyle = false;
        if (selectedStyle) {
          isLogoStyle = VectorImageStyleGroups.logos.styles.some(
            style => style.option === selectedStyle.option,
          );
        }

        if (isLogoStyle) {
          const logoSize = 256;
          const scaleX = logoSize / imageWidth;
          const scaleY = logoSize / imageHeight;
          const scale = Math.min(scaleX, scaleY);

          konvaImage.scaleX(scale);
          konvaImage.scaleY(scale);
          konvaImage.x((canvasWidth - imageWidth * scale) / 2);
          konvaImage.y((canvasHeight - imageHeight * scale) / 2);
        } else {
          const scaleX = canvasWidth / imageWidth;
          const scaleY = canvasHeight / imageHeight;
          const scale = Math.min(scaleX, scaleY);

          konvaImage.scaleX(scale);
          konvaImage.scaleY(scale);
          konvaImage.x((canvasWidth - imageWidth * scale) / 2);
          konvaImage.y((canvasHeight - imageHeight * scale) / 2);
        }

        addCursorHandlers(konvaImage);

        layer.add(konvaImage);

        let transformer = layer.findOne('Transformer') as Konva.Transformer;
        if (!transformer) {
          transformer = new Konva.Transformer();
          layer.add(transformer);
        }

        transformer.nodes([konvaImage]);
        canvas.batchDraw();
        URL.revokeObjectURL(svgUrl);
      };
      imageObj.src = svgUrl;

      if (activeProject?.project_id && agentId) {
        const fileName = `Vector Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
        projectImageStorage.addGeneratedImage(
          activeProject.project_id,
          agentId,
          svgUrl,
          fileName,
          planId,
          imagePrompt,
        ).then(() => {
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }).catch((error) => {
          console.error('Error storing vector image:', error);
        });
      }

      trackContentEvent('image', {
        prompt: imagePrompt,
        imageStyle: selectedStyle?.option || 'none',
      });
    } catch (error: unknown) {
      console.error('Error adding SVG to canvas:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add vector image to canvas';
      toast.error(errorMessage);
    }
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Vector Style</h3>
            <p className="text-gray-400 text-sm">Create vector illustrations using AI</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className="pr-2">
                {Object.entries(VectorImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className="relative rounded-2xl transition-all duration-300 text-left group aspect-square bg-gradient-to-br from-neutral-800/80 to-neutral-900/80 backdrop-blur-sm hover:from-neutral-700/90 hover:to-neutral-800/90 hover:scale-[1.02] hover:shadow-xl hover:shadow-black/20 active:scale-[0.98]"
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex-1 relative overflow-hidden rounded-xl">
                              <div className="relative h-full w-full rounded-xl overflow-hidden">
                                <Image
                                  src={`/images/vector_styles/${style.option}.png`}
                                  alt={style.label}
                                  width={200}
                                  height={200}
                                  quality={30}
                                  className='rounded-xl h-full w-full object-cover transition-transform duration-300 group-hover:scale-105'
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = '/images/vector_styles/vectorIllustration.png';
                                  }}
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
                              </div>
                            </div>
                            <div className="text-[10px] text-center absolute bottom-3 left-3 right-3 text-white bg-black/60 rounded-lg py-1.5 px-2.5 backdrop-blur-sm truncate shadow-lg border border-white/10 group-hover:bg-black/70 transition-all duration-300">
                              {style.label}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : currentStep === 'details' ? (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Describe Your Vector Image</h3>
                <p className="text-gray-400 text-sm">
                Style: <span className="text-violets-are-blue">{selectedStyle?.label}</span>
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col space-y-4">
            <label htmlFor='image-prompt' className="text-white font-medium text-sm">
              Image Description
              <TextArea
                id="image-prompt"
                name="vector-image-prompt"
                ref={imagePromptRef}
                value={imagePrompt}
                width='w-full'
                onChange={(e) => setImagePrompt(e.target.value)}
                placeholder="Describe the vector image you want to create..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey && !loadingStates.isGenerating && imagePrompt.trim()) {
                    e.preventDefault();
                    handleGenerate();
                  }
                }}
              />
            </label>

            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />
              </div>
            </div>
            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Artistic Level: {artisticLevel}
              </label>
              <input
                type="range"
                min="0"
                max="5"
                step="1"
                value={artisticLevel}
                onChange={(e) => setArtisticLevel(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Preferred Colors
              </label>
              <ColorSelector
                selectedColors={selectedColors}
                onColorsChange={setSelectedColors}
              />
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Background Color
              </label>
              <BackgroundColorSelector
                selectedColor={backgroundColor}
                onColorChange={setBackgroundColor}
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              variant="gradient"
              size="md"
              width="w-full"
              onClick={handleGenerate}
              disabled={loadingStates.isVectorizing || !imagePrompt.trim()}
            >
              {loadingStates.isVectorizing ? 'Generating Vector Image...' : 'Generate Vector Image'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>
        </>
      ) : null}
    </div>
  );
};
